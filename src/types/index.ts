// TypeScript Type Definitions
// Export types as they are organized

// Core entity types
export type {
  User,
  Document,
  Collection,
  KnowledgeBase,
  KnowledgeBaseSettings,
  DashboardDocument,
  StatCard,
  ProgressCard,
  GraphCard,
  ListCard,
  InfoCard,
  CollectionData,
} from './entities';

// API-related types
export type {
  ApiResponse,
  ApiError,
  PaginationInfo,
  PaginatedResponse,
  ApiRequestConfig,
  UploadProgress,
  FileUploadResponse,
  QueryRequest,
  QueryResponse,
} from './api';

// Component prop interfaces
export type {
  BaseComponentProps,
  ContainerProps,
  GridProps,
  ButtonProps,
  InputProps,
  CardProps,
  ModalProps,
  IconProps,
  ToastProps,
  LoadingSpinnerProps,
  FormFieldProps,
  TableColumn,
  TableProps,
  FileUploadProps,
  SearchInputProps,
  DashboardProps,
  FileSystemProps,
  AnimatedToggleProps,
  FileQueryProps,
  FileUploadStatusProps,
  QuickAction,
  QueryInputProps,
  OnboardingTourProps,
  ChartLineCardProps,
  ChartDonutCardProps,
  ChartBarCardProps,
  GraphsRowProps,
  DashboardLayoutProps,
  ShimmerProps,
  ParticlesProps,
  ThreadsProps,
  TiltedCardProps,
} from './components';

// Store-related types
export type {
  FileMeta,
  FileUploadState,
  FileQueryHistory,
  UIState,
  Notification,
} from './stores';

// Error handling types
export type {
  AppError,
  ValidationError,
  NetworkError,
  FileError,
  AuthError,
  ErrorBoundaryState,
  ErrorBoundaryProps,
  ErrorFallbackProps,
  ErrorHandler,
  AsyncErrorHandler,
  ErrorSeverity,
  ErrorCategory,
  EnhancedError,
  ErrorReportingConfig,
  ErrorToastOptions,
} from './errors';