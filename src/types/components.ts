// Component prop interfaces and base types

import { ReactNode, HTMLAttributes, ButtonHTMLAttributes, InputHTMLAttributes } from 'react';

// Base component props that most components should extend
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  id?: string;
  'data-testid'?: string;
}

// Layout component props
export interface ContainerProps extends BaseComponentProps {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  centered?: boolean;
}

export interface GridProps extends BaseComponentProps {
  columns?: number | { sm?: number; md?: number; lg?: number; xl?: number };
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  rows?: number;
}

// UI component props
export interface ButtonProps extends BaseComponentProps, Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'className'> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
}

export interface InputProps extends BaseComponentProps, Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outline';
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  required?: boolean;
}

export interface CardProps extends BaseComponentProps {
  variant?: 'default' | 'outlined' | 'elevated';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  header?: ReactNode;
  footer?: ReactNode;
  hoverable?: boolean;
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
}

// Icon component props
export interface IconProps extends Omit<HTMLAttributes<SVGElement>, 'className'> {
  size?: number | string;
  color?: string;
  className?: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
}

// Toast/Notification props
export interface ToastProps extends BaseComponentProps {
  type?: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  closable?: boolean;
  onClose?: () => void;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Loading component props
export interface LoadingSpinnerProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  text?: string;
}

// Form component props
export interface FormFieldProps extends BaseComponentProps {
  name: string;
  label?: string;
  required?: boolean;
  error?: string;
  helperText?: string;
}

// Table component props
export interface TableColumn<T = any> {
  key: keyof T | string;
  title: string;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  render?: (value: any, record: T, index: number) => ReactNode;
}

export interface TableProps<T = any> extends BaseComponentProps {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  emptyText?: string;
  rowKey?: keyof T | ((record: T) => string);
  onRowClick?: (record: T, index: number) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

// Feature-specific component props
export interface FileUploadProps extends BaseComponentProps {
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  maxFiles?: number;
  onUpload: (files: File[]) => void;
  onProgress?: (progress: number) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  dragAndDrop?: boolean;
}

export interface SearchInputProps extends Omit<InputProps, 'type'> {
  onSearch: (query: string) => void;
  onClear?: () => void;
  suggestions?: string[];
  showSuggestions?: boolean;
  debounceMs?: number;
}

// Dashboard component props
export interface DashboardProps {
  data: {
    cards: any[];
    documents: any[];
  };
  onCollectionSelect: (key: string) => void;
}

// File system component props
export interface FileSystemProps {
  theme?: string;
}

export interface AnimatedToggleProps {
  activeMode: "upload" | "query";
  onModeChange: (mode: "upload" | "query") => void;
  theme?: string;
}

export interface FileQueryProps {
  theme?: string;
}

export interface FileUploadStatusProps {
  status: "pending" | "running" | "completed" | null;
  bundleStatus: any;
  error?: string | null;
}

// Query component props
export interface QuickAction {
  icon: React.ReactNode;
  text: string;
  action: () => void;
}

export interface QueryInputProps {
  query: string;
  setQuery: (query: string) => void;
  onSubmit: (query: string) => void;
  loading?: boolean;
  placeholder?: string;
  quickActions?: QuickAction[];
}

// Onboarding component props
export interface OnboardingTourProps {
  isVisible: boolean;
  onComplete: () => void;
  steps?: any[];
}

// Chart/Graph component props
export interface ChartLineCardProps {
  data?: any[];
  loading?: boolean;
  isDummy?: boolean;
  title?: string;
}

export interface ChartDonutCardProps {
  data?: any[];
  loading?: boolean;
  isDummy?: boolean;
  title?: string;
}

export interface ChartBarCardProps {
  data?: any[];
  loading?: boolean;
  isDummy?: boolean;
  title?: string;
}

export interface GraphsRowProps {
  chartData?: {
    line?: { key: string; data: any[] };
    donut?: { key: string; data: any[] };
    bar?: { key: string; data: any[] };
  };
  loading?: boolean;
}

// Layout component props
export interface DashboardLayoutProps {
  children: React.ReactNode;
}

// UI utility component props
export interface ShimmerProps {
  width?: number | string;
  height?: number | string;
  className?: string;
  rounded?: boolean;
}

export interface ParticlesProps {
  id?: string;
  className?: string;
  quantity?: number;
  staticity?: number;
  ease?: number;
  refresh?: boolean;
}

// 3D/WebGL component props
export interface ThreadsProps {
  color?: [number, number, number];
  amplitude?: number;
  frequency?: number;
  speed?: number;
}

export interface TiltedCardProps {
  imageSrc: React.ComponentProps<"img">["src"];
  altText?: string;
  title?: string;
  description?: string;
}