// Core entity types for the application

export interface User {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  role?: 'admin' | 'user' | 'viewer';
  createdAt: Date;
  updatedAt: Date;
}

export interface Document {
  id: string;
  name: string;
  type: string;
  size: string;
  owner: User;
  ownerAvatar?: string;
  lastModified: Date;
  lastSync?: string;
  status: 'active' | 'archived' | 'processing' | 'syncing' | 'error';
  statusColor?: string;
  icon?: string;
  content?: string;
  metadata?: Record<string, any>;
  tags?: string[];
}

// Dashboard-specific types (migrated from app/types/db-knowladge.type.ts)
export interface DashboardDocument {
  icon: string;
  name: string;
  status: string;
  statusColor: string;
  size: string;
  owner: string;
  ownerAvatar: string;
  lastSync: string;
}

// Dashboard card types
export interface StatCard {
  type: "stat";
  title: string;
  value: string | number;
  subtext?: string;
}

export interface ProgressCard {
  type: "progress";
  title: string;
  value: number; // percent
  subtext?: string;
}

export interface GraphCard {
  type: "graph";
  title: string;
  data: number[];
  subtext?: string;
}

export interface ListCard {
  type: "list";
  title: string;
  items: string[];
}

export type InfoCard = StatCard | ProgressCard | GraphCard | ListCard;

export interface CollectionData {
  title: string;
  icon: string;
  documents: DashboardDocument[];
  cards: InfoCard[];
}

export interface Collection {
  id: string;
  title: string;
  description?: string;
  icon: string;
  documents: Document[];
  owner: User;
  createdAt: Date;
  updatedAt: Date;
  isPublic: boolean;
}

export interface KnowledgeBase {
  id: string;
  name: string;
  description?: string;
  collections: Collection[];
  owner: User;
  collaborators: User[];
  settings: KnowledgeBaseSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface KnowledgeBaseSettings {
  isPublic: boolean;
  allowCollaboration: boolean;
  autoSync: boolean;
  retentionDays?: number;
}