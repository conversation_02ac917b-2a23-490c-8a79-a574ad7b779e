// Tour demonstration data - shows sample database query results
export const tourSampleData = [
  {
    employee_id: 1001,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    department: "Sales",
    position: "Sales Manager",
    salary: 75000,
    hire_date: "2022-03-15",
    performance_rating: 4.5,
    projects_completed: 12,
    location: "New York",
    status: "Active",
  },
  {
    employee_id: 1002,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    department: "Marketing",
    position: "Marketing Specialist",
    salary: 65000,
    hire_date: "2023-01-10",
    performance_rating: 4.2,
    projects_completed: 8,
    location: "Los Angeles",
    status: "Active",
  },
  {
    employee_id: 1003,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    department: "Engineering",
    position: "Senior Developer",
    salary: 95000,
    hire_date: "2021-08-22",
    performance_rating: 4.8,
    projects_completed: 18,
    location: "San Francisco",
    status: "Active",
  },
  {
    employee_id: 1004,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    department: "HR",
    position: "HR Coordinator",
    salary: 55000,
    hire_date: "2023-06-05",
    performance_rating: 4.0,
    projects_completed: 6,
    location: "Chicago",
    status: "Active",
  },
  {
    employee_id: 1005,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    department: "Sales",
    position: "Sales Representative",
    salary: 60000,
    hire_date: "2022-11-12",
    performance_rating: 4.3,
    projects_completed: 10,
    location: "Boston",
    status: "Active",
  },
  {
    employee_id: 1006,
    first_name: "Lisa",
    last_name: "Anderson",
    department: "Marketing",
    position: "Marketing Manager",
    salary: 80000,
    hire_date: "2021-12-03",
    performance_rating: 4.6,
    projects_completed: 15,
    location: "Seattle",
    status: "Active",
  },
  {
    employee_id: 1007,
    first_name: "Robert",
    last_name: "Taylor",
    department: "Engineering",
    position: "QA Engineer",
    salary: 70000,
    hire_date: "2023-03-20",
    performance_rating: 4.1,
    projects_completed: 9,
    location: "Austin",
    status: "Active",
  },
  {
    employee_id: 1008,
    first_name: "Jennifer",
    last_name: "Martinez",
    department: "Finance",
    position: "Financial Analyst",
    salary: 72000,
    hire_date: "2022-07-14",
    performance_rating: 4.4,
    projects_completed: 11,
    location: "Miami",
    status: "Active",
  },
  {
    employee_id: 1009,
    first_name: "Christopher",
    last_name: "Garcia",
    department: "Sales",
    position: "Sales Director",
    salary: 90000,
    hire_date: "2021-05-08",
    performance_rating: 4.7,
    projects_completed: 20,
    location: "Denver",
    status: "Active",
  },
  {
    employee_id: 1010,
    first_name: "Amanda",
    last_name: "Rodriguez",
    department: "Engineering",
    position: "Frontend Developer",
    salary: 75000,
    hire_date: "2023-09-01",
    performance_rating: 4.3,
    projects_completed: 7,
    location: "Portland",
    status: "Active",
  },
  {
    employee_id: 1011,
    first_name: "James",
    last_name: "Lee",
    department: "Marketing",
    position: "Content Creator",
    salary: 58000,
    hire_date: "2022-12-18",
    performance_rating: 4.0,
    projects_completed: 8,
    location: "Nashville",
    status: "Active",
  },
  {
    employee_id: 1012,
    first_name: "Michelle",
    last_name: "White",
    department: "HR",
    position: "HR Manager",
    salary: 85000,
    hire_date: "2021-10-25",
    performance_rating: 4.5,
    projects_completed: 14,
    location: "Phoenix",
    status: "Active",
  },
];

// Sample query history for tour demonstration
export const tourQueryHistory = [
  {
    question: "Show me all employees in the sales department",
    query: "SELECT * FROM employees WHERE department = 'Sales'",
    results_summary: "Found 3 employees in Sales department",
    timestamp: "2024-01-15 14:30:22",
  },
  {
    question: "What is the average salary by department?",
    query:
      "SELECT department, AVG(salary) as avg_salary FROM employees GROUP BY department",
    results_summary: "Average salaries calculated for 5 departments",
    timestamp: "2024-01-15 13:45:10",
  },
  {
    question: "List employees with performance rating above 4.5",
    query:
      "SELECT * FROM employees WHERE performance_rating > 4.5 ORDER BY performance_rating DESC",
    results_summary: "Found 4 employees with high performance ratings",
    timestamp: "2024-01-15 12:20:15",
  },
  {
    question: "List employees with performance rating above 4.5",
    query:
      "SELECT * FROM employees WHERE performance_rating > 4.5 ORDER BY performance_rating DESC",
    results_summary: "Found 4 employees with high performance ratings",
    timestamp: "2024-01-15 12:20:15",
  },
  {
    question: "What is the average salary by department?",
    query:
      "SELECT department, AVG(salary) as avg_salary FROM employees GROUP BY department",
    results_summary: "Average salaries calculated for 5 departments",
    timestamp: "2024-01-15 13:45:10",
  },
];

// Mock API response for tour
export const tourApiResponse = {
  payload: {
    data: tourSampleData,
  },
};
