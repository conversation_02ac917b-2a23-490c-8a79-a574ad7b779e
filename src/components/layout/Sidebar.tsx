"use client";
import { useState, useEffect } from "react";
import Image from "next/image";
import { CardContainer } from "@/components/glass-ui/CardContainer";
import { PrimaryButton } from "@/components/glass-ui/buttons/PrimaryButton";
import { SecondaryButton } from "@/components/glass-ui/buttons/SecondaryButton";
import { useTheme } from "next-themes";

interface SidebarProps {
  selected: string;
  setSelected: (key: string) => void;
  collections: Array<{
    key: string;
    name: string;
    sidebarIcon: React.ComponentType<any>;
  }>;
  quickActions: Array<{
    icon: React.ReactNode;
    text: string;
  }>;
  onQuickActionClick?: (text: string) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  selected,
  setSelected,
  collections,
  quickActions,
  onQuickActionClick,
}) => {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Ensure theme is properly typed for the logo
  const currentTheme = theme as "light" | "dark" | undefined;
  // Fallback theme for logo display
  const logoTheme = currentTheme || "dark";

  if (!mounted) return null;

  return (
    <aside className="w-full">
      {/* Logo at the top */}
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          margin: "2px 0 24px 0",
          scale: "1.3",
        }}
      >
        <Image
          src={
            logoTheme === "dark"
              ? "/logo/ESAP_W.png"
              : "/logo/ESAP_B_PNG.png"
          }
          alt="ESAP Logo"
          width={180}
          height={60}
          priority
          style={{
            objectFit: "contain",
            maxWidth: 180,
            height: "auto",
          }}
        />
      </div>
      <CardContainer>
        {/* Tab List */}
        <div
          className="sidebar-tab-list"
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "stretch",
            gap: "20px",
            width: "100%",
          }}
        >
          {collections.map((col) => (
            <div key={col.key}>
              {selected === col.key ? (
                <PrimaryButton
                  icon={
                    (<col.sidebarIcon fill="#5BE49B" />) as unknown as string
                  }
                  key={col.key}
                  text={col.name}
                  onClick={() => setSelected(col.key)}
                  className="w-full h-12"
                  mode={theme === "dark" ? "dark" : "light"}
                />
              ) : (
                <div
                  onClick={() => setSelected(col.key)}
                  style={{
                    display: "flex",
                    minHeight: "44px",
                    padding: "4px 8px 4px 12px",
                    alignItems: "center",
                    alignSelf: "stretch",
                    borderRadius: "8px",
                    background:
                      theme === "dark"
                        ? "rgba(255,255,255,0.00)"
                        : "rgba(0,0,0,0.00)",
                    cursor: "pointer",
                    fontWeight: 500,
                    fontSize: "15px",
                    color: theme === "dark" ? "#fff" : "#222",
                    transition: "background 0.2s, color 0.2s",
                    gap: "10px",
                  }}
                  onMouseEnter={(e) => {
                    if (theme !== "dark")
                      e.currentTarget.style.background = "#f3f4f6";
                  }}
                  onMouseLeave={(e) => {
                    if (theme !== "dark")
                      e.currentTarget.style.background = "rgba(0,0,0,0.00)";
                  }}
                >
                  <div style={{ marginRight: "10px" }}>
                    <col.sidebarIcon
                      fill={theme === "dark" ? "#fff" : "#222"}
                    />
                  </div>
                  {col.name}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Quick Actions Section */}
        <div className="mt-8">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">
            Quick Actions
          </h3>
          <div className="flex flex-col gap-3">
            {quickActions.map((action, index) => (
              <SecondaryButton
                key={index}
                icon={action.icon as unknown as string}
                text={action.text}
                onClick={() => onQuickActionClick?.(action.text)}
                className="w-full h-12 text-left"
                style={{}}
                mode={theme === "dark" ? "dark" : "light"}
              />
            ))}
          </div>
        </div>
      </CardContainer>
    </aside>
  );
};