"use client";
import React from "react";
import { PageTransition } from "@/components/ui/opening-animation";
import { Sidebar } from "./Sidebar";

interface AppSidebarProps {
  selected: string;
  setSelected: (key: string) => void;
  collections: Array<{
    key: string;
    name: string;
    sidebarIcon: React.ComponentType<any>;
  }>;
  quickActions: Array<{
    icon: React.ReactNode;
    text: string;
  }>;
  onQuickActionClick?: (text: string) => void;
}

export const AppSidebar: React.FC<AppSidebarProps> = (props) => {
  return (
    <PageTransition>
      <Sidebar {...props} />
    </PageTransition>
  );
};