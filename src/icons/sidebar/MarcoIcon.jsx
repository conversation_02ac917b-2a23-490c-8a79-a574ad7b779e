import * as React from "react";

const MarcoIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="40"
    height="42"
    fill="none"
    viewBox="0 0 40 42"
    {...props}
  >
    <foreignObject width="34.284" height="29.547" x="-2.668" y="5.49">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_0_12037_6936_clip_path)"
        style={{
          backdropFilter: "blur(3.18px)",
          height: "100%",
          width: "100%",
        }}
      ></div>
    </foreignObject>
    <g
      data-figma-bg-blur-radius="6.352"
      filter="url(#filter0_iiiii_12037_6936)"
    >
      <rect
        width="21.579"
        height="16.842"
        x="3.684"
        y="11.842"
        fill="#fff"
        fillOpacity="0.107"
        rx="4.474"
      ></rect>
    </g>
    <foreignObject width="4.172" height="8.862" x="24.427" y="16.401">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_1_12037_6936_clip_path)"
        style={{ backdropFilter: "blur(.35px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g
      data-figma-bg-blur-radius="0.704"
      filter="url(#filter1_iiiii_12037_6936)"
    >
      <path
        fill="#D9D9D9"
        fillOpacity="0.153"
        d="M25.132 17.105c.363 0 .722.092 1.057.27.335.18.64.44.897.77.256.33.46.722.599 1.153s.21.893.21 1.36c0 .466-.072.928-.21 1.36a3.8 3.8 0 0 1-.6 1.152c-.256.33-.56.592-.896.77s-.694.27-1.057.27v-7.105"
      ></path>
    </g>
    <foreignObject width="4.172" height="8.862" x="0.348" y="16.401">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_2_12037_6936_clip_path)"
        style={{ backdropFilter: "blur(.35px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g
      data-figma-bg-blur-radius="0.704"
      filter="url(#filter2_iiiii_12037_6936)"
    >
      <path
        fill="#D9D9D9"
        fillOpacity="0.153"
        d="M3.816 24.21c-.363 0-.722-.091-1.057-.27a2.8 2.8 0 0 1-.897-.77 3.8 3.8 0 0 1-.599-1.153 4.5 4.5 0 0 1-.21-1.36c0-.466.071-.928.21-1.359s.343-.822.6-1.152.56-.592.896-.77a2.24 2.24 0 0 1 1.057-.27v7.104"
      ></path>
    </g>
    <foreignObject width="23.463" height="16.094" x="2.743" y="12.479">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_3_12037_6936_clip_path)"
        style={{
          backdropFilter: "blur(1.52px)",
          height: "100%",
          width: "100%",
        }}
      ></div>
    </foreignObject>
    <g data-figma-bg-blur-radius="3.047" filter="url(#filter3_iii_12037_6936)">
      <rect
        width="17.368"
        height="10"
        x="5.79"
        y="15.526"
        fill="#fff"
        fillOpacity="0.085"
        rx="3.421"
      ></rect>
    </g>
    <foreignObject width="4.838" height="3.818" x="11.924" y="21.124">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_4_12037_6936_clip_path)"
        style={{ backdropFilter: "blur(.28px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g
      data-figma-bg-blur-radius="0.557"
      filter="url(#filter4_iiiii_12037_6936)"
    >
      <path
        fill="#D9D9D9"
        fillOpacity="0.153"
        d="M15.454 21.681c.476 0 .877.398.712.845-.1.268-.245.511-.428.716s-.4.367-.64.479a1.8 1.8 0 0 1-1.511 0 2 2 0 0 1-.64-.479 2.2 2.2 0 0 1-.428-.716c-.165-.447.236-.845.712-.845h2.222"
      ></path>
    </g>
    <foreignObject width="4.618" height="4.941" x="6.901" y="17.427">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_5_12037_6936_clip_path)"
        style={{ backdropFilter: "blur(.37px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g data-figma-bg-blur-radius="0.73" filter="url(#filter5_iiiii_12037_6936)">
      <circle
        cx="9.211"
        cy="19.737"
        r="1.579"
        fill="#D9D9D9"
        fillOpacity="0.151"
      ></circle>
    </g>
    <foreignObject width="4.618" height="4.941" x="17.165" y="17.427">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_6_12037_6936_clip_path)"
        style={{ backdropFilter: "blur(.37px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g data-figma-bg-blur-radius="0.73" filter="url(#filter6_iiiii_12037_6936)">
      <circle
        cx="19.474"
        cy="19.737"
        r="1.579"
        fill="#D9D9D9"
        fillOpacity="0.151"
      ></circle>
    </g>
    <foreignObject width="7.688" height="11.647" x="22.008" y="28.256">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_7_12037_6936_clip_path)"
        style={{ backdropFilter: "blur(.95px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g
      data-figma-bg-blur-radius="1.904"
      filter="url(#filter7_iiiii_12037_6936)"
    >
      <ellipse
        cx="25.852"
        cy="34.079"
        fill="#D9D9D9"
        fillOpacity="0.199"
        rx="1.669"
        ry="4.042"
        transform="rotate(-15.595 25.852 34.079)"
      ></ellipse>
    </g>
    <foreignObject width="7.677" height="11.653" x="-0.629" y="28.251">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_8_12037_6936_clip_path)"
        style={{ backdropFilter: "blur(.95px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g
      data-figma-bg-blur-radius="1.904"
      filter="url(#filter8_iiiii_12037_6936)"
    >
      <ellipse
        cx="3.209"
        cy="34.077"
        fill="#D9D9D9"
        fillOpacity="0.199"
        rx="1.669"
        ry="4.042"
        transform="rotate(15.4 3.209 34.077)"
      ></ellipse>
    </g>
    <path
      fill="#FF87FD"
      d="M17.895 32.237c.726 0 1.328.593 1.202 1.31q-.095.541-.267 1.056a6.4 6.4 0 0 1-1.084 2.007 5.1 5.1 0 0 1-1.622 1.34c-.607.311-1.257.471-1.914.471-.656 0-1.306-.16-1.913-.47a5.1 5.1 0 0 1-1.622-1.341 6.4 6.4 0 0 1-1.084-2.007 7 7 0 0 1-.267-1.057c-.126-.716.476-1.31 1.202-1.31h7.369"
    ></path>
    <foreignObject width="25.545" height="18.017" x="1.701" y="25.86">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_9_12037_6936_clip_path)"
        style={{
          backdropFilter: "blur(1.41px)",
          height: "100%",
          width: "100%",
        }}
      ></div>
    </foreignObject>
    <g
      data-figma-bg-blur-radius="2.825"
      filter="url(#filter9_iiiii_12037_6936)"
    >
      <path
        fill="#D9D9D9"
        fillOpacity="0.153"
        d="M23.158 28.684c.726 0 1.322.59 1.259 1.314a14.8 14.8 0 0 1-.705 3.42c-.502 1.5-1.239 2.864-2.167 4.012-.929 1.148-2.031 2.06-3.245 2.681a8.4 8.4 0 0 1-3.826.942 8.4 8.4 0 0 1-3.827-.942C9.434 39.49 8.33 38.58 7.403 37.43c-.929-1.148-1.666-2.512-2.168-4.013a14.8 14.8 0 0 1-.705-3.419c-.062-.724.533-1.314 1.26-1.314h17.368"
      ></path>
    </g>
    <foreignObject width="7.733" height="7.47" x="10.607" y="29.554">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_10_12037_6936_clip_path)"
        style={{ backdropFilter: "blur(.75px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g
      data-figma-bg-blur-radius="1.498"
      filter="url(#filter10_iiiii_12037_6936)"
    >
      <ellipse
        cx="14.473"
        cy="33.289"
        fill="#D9D9D9"
        fillOpacity="0.152"
        rx="2.368"
        ry="2.237"
      ></ellipse>
    </g>
    <foreignObject width="29.123" height="23.313" x="11.092" y="-3.663">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_11_12037_6936_clip_path)"
        style={{
          backdropFilter: "blur(2.49px)",
          height: "100%",
          width: "100%",
        }}
      ></div>
    </foreignObject>
    <g
      data-figma-bg-blur-radius="4.978"
      filter="url(#filter11_iiiii_12037_6936)"
    >
      <path
        fill="url(#paint0_linear_12037_6936)"
        fillOpacity="0.158"
        d="M16.285 4.454c.104-1.234.156-1.851.556-2.242.4-.39 1.018-.428 2.254-.503l6.513-.393 6.56.396c1.216.074 1.823.11 2.221.492.398.381.46.987.585 2.198l.184 1.794c.053.511.079.767.079 1.023s-.026.512-.079 1.023l-.177 1.723c-.128 1.242-.192 1.864-.603 2.248-.412.384-1.036.404-2.284.445l-6.486.214-4.572-.15c-.248-.009-.372-.013-.48.037-.109.05-.185.148-.339.343l-.72.913c-.37.471-.556.707-.725.648s-.168-.358-.168-.957V13.2c0-.07 0-.106-.003-.136a.5.5 0 0 0-.421-.434 1 1 0 0 0-.135-.008c-.195-.007-.293-.01-.377-.022a1.5 1.5 0 0 1-1.253-1.191 4 4 0 0 1-.041-.376l-.25-2.976c-.036-.42-.054-.629-.054-.839s.018-.42.053-.839z"
      ></path>
    </g>
    <foreignObject width="18.779" height="13.706" x="21.442" y="4.599">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        clipPath="url(#bgblur_12_12037_6936_clip_path)"
        style={{ backdropFilter: "blur(.46px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g data-figma-bg-blur-radius="0.927" filter="url(#filter12_i_12037_6936)">
      <path
        fill="url(#paint1_linear_12037_6936)"
        d="M22.478 7.913c.036-.974.054-1.461.36-1.77.307-.308.794-.329 1.768-.37l5.79-.247 6.645.255c.972.038 1.459.056 1.766.363.308.306.33.792.371 1.764l.116 2.68-.134 3.503c-.028.723-.042 1.084-.219 1.347a1.1 1.1 0 0 1-.318.313c-.266.171-.627.179-1.35.193-.25.005-.375.007-.465.068a.4.4 0 0 0-.107.108c-.058.092-.058.217-.058.466v.178c0 .37 0 .556-.113.604-.112.048-.246-.08-.514-.336l-.848-.812c-.118-.113-.177-.17-.252-.196-.074-.026-.156-.02-.318-.007l-4.332.335-5.691-.328c-.96-.055-1.44-.083-1.741-.39-.301-.308-.319-.788-.354-1.75l-.111-3.037z"
      ></path>
    </g>
    <foreignObject width="15.849" height="4.533" x="22.602" y="6.023">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        style={{ backdropFilter: "blur(.94px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g data-figma-bg-blur-radius="1.872" filter="url(#filter13_ii_12037_6936)">
      <path
        stroke="#fff"
        strokeOpacity="0.1"
        strokeWidth="0.789"
        d="M24.474 8.289h12.105"
      ></path>
    </g>
    <foreignObject width="14.006" height="4.533" x="24.444" y="8.392">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        style={{ backdropFilter: "blur(.94px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g data-figma-bg-blur-radius="1.872" filter="url(#filter14_ii_12037_6936)">
      <path
        stroke="#fff"
        strokeOpacity="0.1"
        strokeWidth="0.789"
        d="M26.316 10.658h10.263"
      ></path>
    </g>
    <foreignObject width="11.112" height="4.533" x="27.339" y="10.76">
      <div
        xmlns="http://www.w3.org/1999/xhtml"
        style={{ backdropFilter: "blur(.94px)", height: "100%", width: "100%" }}
      ></div>
    </foreignObject>
    <g data-figma-bg-blur-radius="1.872" filter="url(#filter15_ii_12037_6936)">
      <path
        stroke="#fff"
        strokeOpacity="0.1"
        strokeWidth="0.789"
        d="M29.211 13.026h7.368"
      ></path>
    </g>
    <defs>
      <filter
        id="filter0_iiiii_12037_6936"
        width="34.284"
        height="29.547"
        x="-2.668"
        y="5.49"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.512" dy="0.512"></feOffset>
        <feGaussianBlur stdDeviation="0.256"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.8 0 0 0 0 0.8 0 0 0 0 0.8 0 0 0 0.414 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.256" dy="0.256"></feOffset>
        <feGaussianBlur stdDeviation="0.128"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.414 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.512" dy="-0.512"></feOffset>
        <feGaussianBlur stdDeviation="0.256"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.414 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.256" dy="-0.256"></feOffset>
        <feGaussianBlur stdDeviation="0.128"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.8 0 0 0 0 0.8 0 0 0 0 0.8 0 0 0 0.414 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="3.947"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="0.526"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter1_iiiii_12037_6936"
        width="4.172"
        height="8.862"
        x="24.427"
        y="16.401"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.141" dy="0.141"></feOffset>
        <feGaussianBlur stdDeviation="0.07"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.506 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.07" dy="0.07"></feOffset>
        <feGaussianBlur stdDeviation="0.035"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.141" dy="-0.141"></feOffset>
        <feGaussianBlur stdDeviation="0.07"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.07" dy="-0.07"></feOffset>
        <feGaussianBlur stdDeviation="0.035"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="3.947"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="0.526"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.117647 0 0 0 0 0.117647 0 0 0 0 0.117647 0 0 0 0.12 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter2_iiiii_12037_6936"
        width="4.172"
        height="8.862"
        x="0.348"
        y="16.401"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.141" dy="0.141"></feOffset>
        <feGaussianBlur stdDeviation="0.07"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.506 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.07" dy="0.07"></feOffset>
        <feGaussianBlur stdDeviation="0.035"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.141" dy="-0.141"></feOffset>
        <feGaussianBlur stdDeviation="0.07"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.07" dy="-0.07"></feOffset>
        <feGaussianBlur stdDeviation="0.035"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="2.632"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="0.526"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.117647 0 0 0 0 0.117647 0 0 0 0 0.117647 0 0 0 0.12 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter3_iii_12037_6936"
        width="23.463"
        height="16.094"
        x="2.743"
        y="12.479"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.912" dy="0.912"></feOffset>
        <feGaussianBlur stdDeviation="0.456"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.085 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.912" dy="-0.912"></feOffset>
        <feGaussianBlur stdDeviation="0.456"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.76 0 0 0 0 0.76 0 0 0 0 0.76 0 0 0 0.085 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="9.211"
          result="effect3_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="0.526"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.178438 0 0 0 0 0.176915 0 0 0 0 0.179155 0 0 0 0.5 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter4_iiiii_12037_6936"
        width="4.838"
        height="3.818"
        x="11.924"
        y="21.124"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.111" dy="0.111"></feOffset>
        <feGaussianBlur stdDeviation="0.056"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.506 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.056" dy="0.056"></feOffset>
        <feGaussianBlur stdDeviation="0.028"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.111" dy="-0.111"></feOffset>
        <feGaussianBlur stdDeviation="0.056"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.056" dy="-0.056"></feOffset>
        <feGaussianBlur stdDeviation="0.028"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="3.947"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="0.526"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.4875 0 0 0 0 0.871875 0 0 0 0 1 0 0 0 0.25 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter5_iiiii_12037_6936"
        width="4.618"
        height="4.941"
        x="6.901"
        y="17.427"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.081" dy="0.081"></feOffset>
        <feGaussianBlur stdDeviation="0.04"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.502 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.04" dy="0.04"></feOffset>
        <feGaussianBlur stdDeviation="0.02"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.502 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.081" dy="-0.081"></feOffset>
        <feGaussianBlur stdDeviation="0.04"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.502 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.04" dy="-0.04"></feOffset>
        <feGaussianBlur stdDeviation="0.02"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.502 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="3.947"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="0.526"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.4875 0 0 0 0 0.871875 0 0 0 0 1 0 0 0 0.25 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter6_iiiii_12037_6936"
        width="4.618"
        height="4.941"
        x="17.165"
        y="17.427"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.081" dy="0.081"></feOffset>
        <feGaussianBlur stdDeviation="0.04"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.502 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.04" dy="0.04"></feOffset>
        <feGaussianBlur stdDeviation="0.02"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.502 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.081" dy="-0.081"></feOffset>
        <feGaussianBlur stdDeviation="0.04"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.502 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.04" dy="-0.04"></feOffset>
        <feGaussianBlur stdDeviation="0.02"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.502 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="3.947"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="0.526"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.4875 0 0 0 0 0.871875 0 0 0 0 1 0 0 0 0.25 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter7_iiiii_12037_6936"
        width="7.688"
        height="11.647"
        x="22.008"
        y="28.256"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.152" dy="0.152"></feOffset>
        <feGaussianBlur stdDeviation="0.076"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.598 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.076" dy="0.076"></feOffset>
        <feGaussianBlur stdDeviation="0.038"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.598 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.152" dy="-0.152"></feOffset>
        <feGaussianBlur stdDeviation="0.076"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.598 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.076" dy="-0.076"></feOffset>
        <feGaussianBlur stdDeviation="0.038"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.598 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="3.947"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="1.316"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter8_iiiii_12037_6936"
        width="7.677"
        height="11.653"
        x="-0.629"
        y="28.251"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.152" dy="0.152"></feOffset>
        <feGaussianBlur stdDeviation="0.076"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.598 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.076" dy="0.076"></feOffset>
        <feGaussianBlur stdDeviation="0.038"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.598 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.152" dy="-0.152"></feOffset>
        <feGaussianBlur stdDeviation="0.076"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.598 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.076" dy="-0.076"></feOffset>
        <feGaussianBlur stdDeviation="0.038"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.598 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="3.947"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="1.316"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter9_iiiii_12037_6936"
        width="25.545"
        height="18.017"
        x="1.701"
        y="25.86"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.565" dy="0.565"></feOffset>
        <feGaussianBlur stdDeviation="0.282"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.506 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.282" dy="0.282"></feOffset>
        <feGaussianBlur stdDeviation="0.141"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.565" dy="-0.565"></feOffset>
        <feGaussianBlur stdDeviation="0.282"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.282" dy="-0.282"></feOffset>
        <feGaussianBlur stdDeviation="0.141"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.506 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="10.526"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="1.974"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter10_iiiii_12037_6936"
        width="7.733"
        height="7.47"
        x="10.607"
        y="29.554"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.123" dy="0.123"></feOffset>
        <feGaussianBlur stdDeviation="0.061"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.504 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.061" dy="0.061"></feOffset>
        <feGaussianBlur stdDeviation="0.031"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.504 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.123" dy="-0.123"></feOffset>
        <feGaussianBlur stdDeviation="0.061"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.504 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.061" dy="-0.061"></feOffset>
        <feGaussianBlur stdDeviation="0.031"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0 0.680784 0 0 0 0.504 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feMorphology
          in="SourceAlpha"
          radius="13.158"
          result="effect5_innerShadow_12037_6936"
        ></feMorphology>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="7.237"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.117647 0 0 0 0 0.117647 0 0 0 0 0.117647 0 0 0 0.55 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter11_iiiii_12037_6936"
        width="29.123"
        height="23.313"
        x="11.092"
        y="-3.663"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.441" dy="0.441"></feOffset>
        <feGaussianBlur stdDeviation="0.22"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.8 0 0 0 0 0.581961 0 0 0 0 0.315294 0 0 0 0.516 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.22" dy="0.22"></feOffset>
        <feGaussianBlur stdDeviation="0.11"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.516 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.441" dy="-0.441"></feOffset>
        <feGaussianBlur stdDeviation="0.22"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.516 0"></feColorMatrix>
        <feBlend
          in2="effect2_innerShadow_12037_6936"
          result="effect3_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.22" dy="-0.22"></feOffset>
        <feGaussianBlur stdDeviation="0.11"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.8 0 0 0 0 0.581961 0 0 0 0 0.315294 0 0 0 0.516 0"></feColorMatrix>
        <feBlend
          in2="effect3_innerShadow_12037_6936"
          result="effect4_innerShadow_12037_6936"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dy="1.053"></feOffset>
        <feGaussianBlur stdDeviation="6.579"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"></feColorMatrix>
        <feBlend
          in2="effect4_innerShadow_12037_6936"
          result="effect5_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter12_i_12037_6936"
        width="18.779"
        height="13.706"
        x="21.442"
        y="4.599"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset></feOffset>
        <feGaussianBlur stdDeviation="1.112"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
      </filter>
      <filter
        id="filter13_ii_12037_6936"
        width="15.849"
        height="4.533"
        x="22.602"
        y="6.023"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.368" dy="0.368"></feOffset>
        <feGaussianBlur stdDeviation="0.184"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.554 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.074" dy="-0.074"></feOffset>
        <feGaussianBlur stdDeviation="0.037"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.84 0 0 0 0 0.84 0 0 0 0 0.84 0 0 0 0.554 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter14_ii_12037_6936"
        width="14.006"
        height="4.533"
        x="24.444"
        y="8.392"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.368" dy="0.368"></feOffset>
        <feGaussianBlur stdDeviation="0.184"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.554 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.074" dy="-0.074"></feOffset>
        <feGaussianBlur stdDeviation="0.037"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.84 0 0 0 0 0.84 0 0 0 0 0.84 0 0 0 0.554 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <filter
        id="filter15_ii_12037_6936"
        width="11.112"
        height="4.533"
        x="27.339"
        y="10.76"
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
        <feBlend
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        ></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="-0.368" dy="0.368"></feOffset>
        <feGaussianBlur stdDeviation="0.184"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.554 0"></feColorMatrix>
        <feBlend in2="shape" result="effect1_innerShadow_12037_6936"></feBlend>
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        ></feColorMatrix>
        <feOffset dx="0.074" dy="-0.074"></feOffset>
        <feGaussianBlur stdDeviation="0.037"></feGaussianBlur>
        <feComposite
          in2="hardAlpha"
          k2="-1"
          k3="1"
          operator="arithmetic"
        ></feComposite>
        <feColorMatrix values="0 0 0 0 0.84 0 0 0 0 0.84 0 0 0 0 0.84 0 0 0 0.554 0"></feColorMatrix>
        <feBlend
          in2="effect1_innerShadow_12037_6936"
          result="effect2_innerShadow_12037_6936"
        ></feBlend>
      </filter>
      <clipPath
        id="bgblur_0_12037_6936_clip_path"
        transform="translate(2.668 -5.49)"
      >
        <rect
          width="21.579"
          height="16.842"
          x="3.684"
          y="11.842"
          rx="4.474"
        ></rect>
      </clipPath>
      <clipPath
        id="bgblur_1_12037_6936_clip_path"
        transform="translate(-24.427 -16.4)"
      >
        <path d="M25.132 17.105c.363 0 .722.092 1.057.27.335.18.64.44.897.77.256.33.46.722.599 1.153s.21.893.21 1.36c0 .466-.072.928-.21 1.36a3.8 3.8 0 0 1-.6 1.152c-.256.33-.56.592-.896.77s-.694.27-1.057.27v-7.105"></path>
      </clipPath>
      <clipPath
        id="bgblur_2_12037_6936_clip_path"
        transform="translate(-.348 -16.4)"
      >
        <path d="M3.816 24.21c-.363 0-.722-.091-1.057-.27a2.8 2.8 0 0 1-.897-.77 3.8 3.8 0 0 1-.599-1.153 4.5 4.5 0 0 1-.21-1.36c0-.466.071-.928.21-1.359s.343-.822.6-1.152.56-.592.896-.77a2.24 2.24 0 0 1 1.057-.27v7.104"></path>
      </clipPath>
      <clipPath
        id="bgblur_3_12037_6936_clip_path"
        transform="translate(-2.743 -12.48)"
      >
        <rect width="17.368" height="10" x="5.79" y="15.526" rx="3.421"></rect>
      </clipPath>
      <clipPath
        id="bgblur_4_12037_6936_clip_path"
        transform="translate(-11.924 -21.124)"
      >
        <path d="M15.454 21.681c.476 0 .877.398.712.845-.1.268-.245.511-.428.716s-.4.367-.64.479a1.8 1.8 0 0 1-1.511 0 2 2 0 0 1-.64-.479 2.2 2.2 0 0 1-.428-.716c-.165-.447.236-.845.712-.845h2.222"></path>
      </clipPath>
      <clipPath
        id="bgblur_5_12037_6936_clip_path"
        transform="translate(-6.901 -17.427)"
      >
        <circle cx="9.211" cy="19.737" r="1.579"></circle>
      </clipPath>
      <clipPath
        id="bgblur_6_12037_6936_clip_path"
        transform="translate(-17.165 -17.427)"
      >
        <circle cx="19.474" cy="19.737" r="1.579"></circle>
      </clipPath>
      <clipPath
        id="bgblur_7_12037_6936_clip_path"
        transform="translate(-22.008 -28.256)"
      >
        <ellipse
          cx="25.852"
          cy="34.079"
          rx="1.669"
          ry="4.042"
          transform="rotate(-15.595 25.852 34.079)"
        ></ellipse>
      </clipPath>
      <clipPath
        id="bgblur_8_12037_6936_clip_path"
        transform="translate(.63 -28.25)"
      >
        <ellipse
          cx="3.209"
          cy="34.077"
          rx="1.669"
          ry="4.042"
          transform="rotate(15.4 3.209 34.077)"
        ></ellipse>
      </clipPath>
      <clipPath
        id="bgblur_9_12037_6936_clip_path"
        transform="translate(-1.701 -25.86)"
      >
        <path d="M23.158 28.684c.726 0 1.322.59 1.259 1.314a14.8 14.8 0 0 1-.705 3.42c-.502 1.5-1.239 2.864-2.167 4.012-.929 1.148-2.031 2.06-3.245 2.681a8.4 8.4 0 0 1-3.826.942 8.4 8.4 0 0 1-3.827-.942C9.434 39.49 8.33 38.58 7.403 37.43c-.929-1.148-1.666-2.512-2.168-4.013a14.8 14.8 0 0 1-.705-3.419c-.062-.724.533-1.314 1.26-1.314h17.368"></path>
      </clipPath>
      <clipPath
        id="bgblur_10_12037_6936_clip_path"
        transform="translate(-10.607 -29.554)"
      >
        <ellipse cx="14.473" cy="33.289" rx="2.368" ry="2.237"></ellipse>
      </clipPath>
      <clipPath
        id="bgblur_11_12037_6936_clip_path"
        transform="translate(-11.092 3.663)"
      >
        <path d="M16.285 4.454c.104-1.234.156-1.851.556-2.242.4-.39 1.018-.428 2.254-.503l6.513-.393 6.56.396c1.216.074 1.823.11 2.221.492.398.381.46.987.585 2.198l.184 1.794c.053.511.079.767.079 1.023s-.026.512-.079 1.023l-.177 1.723c-.128 1.242-.192 1.864-.603 2.248-.412.384-1.036.404-2.284.445l-6.486.214-4.572-.15c-.248-.009-.372-.013-.48.037-.109.05-.185.148-.339.343l-.72.913c-.37.471-.556.707-.725.648s-.168-.358-.168-.957V13.2c0-.07 0-.106-.003-.136a.5.5 0 0 0-.421-.434 1 1 0 0 0-.135-.008c-.195-.007-.293-.01-.377-.022a1.5 1.5 0 0 1-1.253-1.191 4 4 0 0 1-.041-.376l-.25-2.976c-.036-.42-.054-.629-.054-.839s.018-.42.053-.839z"></path>
      </clipPath>
      <clipPath
        id="bgblur_12_12037_6936_clip_path"
        transform="translate(-21.442 -4.6)"
      >
        <path d="M22.478 7.913c.036-.974.054-1.461.36-1.77.307-.308.794-.329 1.768-.37l5.79-.247 6.645.255c.972.038 1.459.056 1.766.363.308.306.33.792.371 1.764l.116 2.68-.134 3.503c-.028.723-.042 1.084-.219 1.347a1.1 1.1 0 0 1-.318.313c-.266.171-.627.179-1.35.193-.25.005-.375.007-.465.068a.4.4 0 0 0-.107.108c-.058.092-.058.217-.058.466v.178c0 .37 0 .556-.113.604-.112.048-.246-.08-.514-.336l-.848-.812c-.118-.113-.177-.17-.252-.196-.074-.026-.156-.02-.318-.007l-4.332.335-5.691-.328c-.96-.055-1.44-.083-1.741-.39-.301-.308-.319-.788-.354-1.75l-.111-3.037z"></path>
      </clipPath>
      <linearGradient
        id="paint0_linear_12037_6936"
        x1="16.828"
        x2="34.288"
        y1="2.191"
        y2="12.397"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#fff"></stop>
        <stop offset="1" stopColor="#fff"></stop>
      </linearGradient>
      <linearGradient
        id="paint1_linear_12037_6936"
        x1="23.036"
        x2="38.589"
        y1="6.045"
        y2="15.778"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FF87FD"></stop>
        <stop offset="1" stopColor="#FF87FD" stopOpacity="0.04"></stop>
      </linearGradient>
    </defs>
  </svg>
);

export default MarcoIcon;
