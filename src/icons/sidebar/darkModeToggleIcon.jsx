import * as React from "react";

const DarkModeToggleIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <path
      fill="#919EAB"
      d="m16.946 11.086.089.004c3.162.128 5.465 2.246 5.465 5.41s-2.303 5.282-5.465 5.41c-1.33.054-2.985.09-5.035.09s-3.706-.036-5.035-.09C3.803 21.782 1.5 19.664 1.5 16.5c0-2.468 1.402-4.3 3.519-5.047C5.241 8.295 7.387 6.024 10.641 6a53 53 0 0 1 .718 0c3.127.022 5.23 2.12 5.587 5.085"
      opacity="0.32"
    ></path>
    <path
      fill="#919EAB"
      d="M19.24 2.289c.286-.169.302-.66-.023-.717a4.78 4.78 0 0 0-3.27.599c-1.535.905-2.408 2.53-2.446 4.217 1.927.739 3.178 2.474 3.445 4.698l.089.004a6.6 6.6 0 0 1 1.97.371 4.8 4.8 0 0 0 1.833-.632 4.95 4.95 0 0 0 2.143-2.595c.114-.316-.31-.548-.596-.38-1.504.888-3.427.362-4.295-1.175-.869-1.538-.353-3.503 1.15-4.39"
    ></path>
  </svg>
);

export default DarkModeToggleIcon;
