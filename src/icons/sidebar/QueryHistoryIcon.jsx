import * as React from "react";

const QueryHistoryIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <g fill="#fff" clipPath="url(#clip0_12037_7372)">
      <path d="M19.266.412v4.276h4.275z"></path>
      <path d="M18.563 6.094a.703.703 0 0 1-.704-.703V0H7.781c-1.163 0-2.11.946-2.11 2.11v7.86q.349-.032.704-.033a7.72 7.72 0 0 1 5.962 2.813h7.632a.703.703 0 0 1 0 1.406h-6.706c.44.858.723 1.808.814 2.813h5.892a.703.703 0 0 1 0 1.406h-5.892A7.74 7.74 0 0 1 10.817 24h11.027c1.163 0 2.11-.946 2.11-2.11V6.095zm1.406 3.843H9.656a.703.703 0 0 1 0-1.406H19.97a.703.703 0 0 1 0 1.406"></path>
      <path d="M6.375 11.344a6.335 6.335 0 0 0-6.328 6.328A6.335 6.335 0 0 0 6.375 24a6.335 6.335 0 0 0 6.328-6.328 6.335 6.335 0 0 0-6.328-6.328m1.875 7.031H6.375a.703.703 0 0 1-.703-.703v-2.813a.703.703 0 0 1 1.406 0v2.11H8.25a.703.703 0 0 1 0 1.406"></path>
    </g>
    <defs>
      <clipPath id="clip0_12037_7372">
        <path fill="#fff" d="M0 0h24v24H0z"></path>
      </clipPath>
    </defs>
  </svg>
);

export default QueryHistoryIcon;