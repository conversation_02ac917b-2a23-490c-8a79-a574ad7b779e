import * as React from "react";

const SupportKnowledgeIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <g fill={props.fill || "#fff"} clipPath="url(#clip0_12037_7361)">
      <path d="M12.436 14.573q.022.001.037-.004c-.014 0-.023 0-.037.004M11.527 14.569q.***************-.021-.005-.042-.004M11.668 14.578a.2.2 0 0 1 .046.005c-.014 0-.033 0-.046-.005M12.75 14.531c.014 0 .023-.004.037-.004-.009 0-.023.004-.037.004M13.041 14.48c.01 0 .019-.005.024-.005q-.009-.001-.024.005M12.89 14.508c.015-.005.029-.005.038-.01a.1.1 0 0 1-.037.01M13.5 14.353c-.01.005-.019.005-.023.01a.1.1 0 0 1 .023-.01M13.364 14.395c-.01.005-.019.005-.028.01.014-.005.019-.01.028-.01M14.04 14.138c-.005 0-.005.004-.01.004q.009-.006.01-.005M13.913 14.194q-.009.002-.**************.01-.004M10.523 14.358c-.009-.005-.018-.005-.023-.01q.008.009.023.01M11.11 14.508c-.014-.005-.024-.005-.038-.01q.016.009.038.01M10.955 14.48c-.01 0-.014-.005-.023-.005q.014-.001.023.005M10.66 14.4c-.01-.005-.02-.005-.029-.01q.015.009.028.01M6.17 6.797c-.109 0-.216.019-.315.047q-.042.347-.042.708v2.001c0 .3-.192.553-.46.642.193.24.483.394.811.394.572 0 1.036-.46 1.036-1.031v-1.73c0-.567-.459-1.031-1.03-1.031M17.831 10.59c.127 0 .244-.024.357-.062V7.556q0-.357-.043-.708a1.036 1.036 0 0 0-1.35.99v1.72c0 .567.464 1.031 1.036 1.031"></path>
      <path d="M5.813 9.553V7.552q0-.358.042-.708c.352-3.075 2.972-5.48 6.145-5.48a6.193 6.193 0 0 1 6.188 6.188v1.767a.685.685 0 0 1 1.369 0V7.552C19.552 3.389 16.163 0 12 0 7.838 0 4.45 3.39 4.45 7.552v2.001a.685.685 0 0 0 .91.647.69.69 0 0 0 .454-.647"></path>
      <path d="m9.966 14.138.127.056h.004c.132.056.263.108.403.15.01.004.02.004.024.01a1 1 0 0 0 .112.032c.01.005.02.005.029.01q.064.018.135.037.07.02.141.033c.01 0 .014.004.023.004.038.01.075.014.113.024.014.005.023.004.037.01.033.004.066.013.104.018.014 0 .028.005.042.005.01 0 .023.004.033.004l.093.014h.01l.14.014c.014 0 .029.005.043.005.032.005.065.005.098.01.014 0 .033 0 .047.004.033 0 .066.005.098.005h.366c.033 0 .066-.005.098-.005.014 0 .033 0 .047-.005q.049 0 .103-.009.022.001.038-.005l.14-.014h.005c.047-.004.09-.014.136-.018.014 0 .023-.005.038-.005.032-.005.07-.014.103-.019.014-.005.028-.005.037-.01.038-.009.075-.013.113-.023q.016-.002.023-.004.106-.022.206-.052c.024-.005.043-.014.066-.019.01-.004.019-.004.028-.01.038-.008.075-.022.113-.032.009-.005.018-.005.023-.01l.127-.041h.004q.135-.05.268-.108c.004 0 .009-.005.009-.005l.122-.056c.005 0 .005-.005.01-.005q.131-.062.257-.131a4.8 4.8 0 0 0 2.498-4.21V7.543a4.767 4.767 0 0 0-1.43-3.413c-.834 1.196-3.238 2.64-8.02 2.241q-.069.267-.103.549a5 5 0 0 0-.042.623v2.246a4.79 4.79 0 0 0 2.756 4.34c0 .01.005.01.005.01M13.5 21.277l-1.439-1.44-1.439 1.44a.91.91 0 0 1-1.289 0l-3.06-3.061-2.987 1.148a1.24 1.24 0 0 0-.797 1.163v2.23c0 .69.558 1.248 1.247 1.248H20.27c.689 0 1.247-.558 1.247-1.247v-2.236a1.24 1.24 0 0 0-.792-1.158l-2.888-1.125-3.042 3.042a.92.92 0 0 1-1.294-.004M9.704 16.045"></path>
      <path d="M13.04 14.48c-.037.01-.074.014-.112.023-.014.005-.023.005-.037.01-.033.004-.07.014-.103.018-.014 0-.024.005-.038.005-.047.005-.089.014-.136.019h-.005l-.14.014q-.022-.001-.038.004c-.032.005-.065.005-.103.01-.014 0-.033 0-.047.005q-.049.002-.098.004h-.366c-.033 0-.065-.005-.098-.005-.014 0-.033 0-.047-.004a1 1 0 0 1-.099-.01q-.021.001-.042-.004l-.14-.014h-.01l-.093-.014q.351.056.717.056c.44 0 .867-.061 1.275-.174-.07.02-.136.038-.207.052q-.02-.001-.032.005"></path>
      <path d="m12.061 19.842 2.794-2.794a1.1 1.1 0 0 1-.211-.178 1 1 0 0 1-.117-.14l-2.27.745a.69.69 0 0 1-.862-.436.683.683 0 0 1 .436-.862l2.466-.811v-1.36a5 5 0 0 1-.258.131c-.005 0-.005.005-.01.005q-.06.029-.121.056c-.005 0-.01.005-.01.005a6 6 0 0 1-.267.108h-.004l-.127.042c-.01.005-.019.005-.023.01q-.056.02-.113.032c-.01.005-.019.005-.028.01-.023.004-.042.014-.066.018a4.8 4.8 0 0 1-1.992.118c-.01 0-.023-.005-.033-.005-.014 0-.028-.005-.042-.005q-.048-.006-.103-.019c-.014-.004-.023-.004-.037-.009-.038-.01-.075-.014-.113-.023-.01 0-.014-.005-.023-.005l-.141-.033c-.047-.01-.089-.023-.136-.037-.01-.005-.019-.005-.028-.01q-.056-.017-.113-.032c-.009-.005-.018-.005-.023-.01a6 6 0 0 1-.403-.15h-.005l-.126-.056s-.005 0-.005-.005a4 4 0 0 1-.258-.131v2.039q0 .234-.084.445c-.08.207-.211.385-.38.525zM12 2.756h-.042A4.8 4.8 0 0 0 7.35 6.38c4.781.398 7.186-1.046 8.02-2.24a4.78 4.78 0 0 0-3.323-1.384z"></path>
      <path d="M18.872 8.64a.685.685 0 0 0-.684.683v2.26a3.45 3.45 0 0 1-2.377 3.281l-1.514.502-2.465.81a.682.682 0 1 0 .427 1.298l2.268-.744 1.71-.563a4.81 4.81 0 0 0 3.315-4.58V9.329a.68.68 0 0 0-.68-.689M6.272 18.216l3.06 3.06a.91.91 0 0 0 1.29 0l1.439-1.439-2.822-2.821q-.148.12-.333.192z"></path>
      <path d="M13.5 21.277a.91.91 0 0 0 1.29 0l3.041-3.043-2.742-1.068a1.4 1.4 0 0 1-.234-.122l-2.794 2.793z"></path>
    </g>
    <defs>
      <clipPath id="clip0_12037_7361">
        <path fill={props.fill || "#fff"} d="M0 0h24v24H0z"></path>
      </clipPath>
    </defs>
  </svg>
);

export default SupportKnowledgeIcon;