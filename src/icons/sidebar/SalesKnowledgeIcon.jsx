import * as React from "react";

const SvgIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <g clipPath="url(#clip0_12037_7349)">
      <path
        fill={props.fill || "#fff"}
        d="m18.94 6.8 4.462 2.825-8.296 1.358-3.107.5-1.14-.722 8.213-1.324c.297-.051.435-.405.294-.626a3.9 3.9 0 0 1-.449-1.658 2 2 0 0 1 .023-.353m-.544 1.898s-9.93 1.607-9.957 1.606c-.055-.002-.088-.11-.087-.209l.003-2.343c.01-.27.142-.387.218-.394l9.763-1.573c-.141.406-.274.926-.255 1.402a4.9 4.9 0 0 0 .315 1.511M11.43.928l6.426 4.07-6.522 1.068-2.589.417-6.419-4.065zM1.004 5.564c-.068-.043-.148-.166-.147-.37l.003-1.96c.007-.24.157-.454.294-.567L7.843 6.9c-.148.128-.327.523-.325.852.002.267-.007 1.93-.007 1.93zM2.51 10.34c-.108-.068-.222-.243-.222-.527 0 0-.038-2.358.01-2.44 0 0 5.544 3.6 5.804 3.703.**************.328.065.087-.002 1.181-.179 1.181-.179l1.431.906c-.366.256-.432.752-.432 1.069l-.008 2.527zm2.287 2.439s6.292 4.042 6.392 4.096a.9.9 0 0 0 .438.109c.052 0 2.12-.33 2.12-.33l-1.739 1.225L.517 15.796zM12.39 23.07 0 20.851c.158-.562.399-1.52.41-2.141.01-.68-.187-1.537-.35-2.122 0 0 11.884 2.079 12.154 **********.356.51.356.687l.003 3.185c0 .156-.053.434-.183.433m9.1-6.457-8.075 5.689-.009-2.85c0-.338-.154-.953-.477-1.198l2.7-1.904 6.098-.982v.606c.001.347-.128.561-.238.639m-9.84-.47c-.062 0-.206-.064-.207-.344l.004-2.863c0-.143.081-.472.345-.574l12.134-1.954c-.151.493-.31 1.17-.267 1.75.063.878.22 1.567.341 1.996 0 0-12.315 1.99-12.35 1.99"
      ></path>
    </g>
    <defs>
      <clipPath id="clip0_12037_7349">
        <path fill="#fff" d="M0 0h24v24H0z"></path>
      </clipPath>
    </defs>
  </svg>
);

export default SvgIcon;
