import * as React from "react";

const HrKnowledgeIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <g clipPath="url(#clip0_12068_1228)">
      <path
        fill={props.fill || "#fff"}
        d="M12 .75A11.25 11.25 0 1 0 23.25 12 11.264 11.264 0 0 0 12 .75m-1.642 2.591a1.657 1.657 0 1 1 .063.672c-4.47.825-7.44 5.565-6.21 9.94a.335.335 0 0 1-.*********** 0 0 1-.158-.203C2.227 9.36 5.479 4.196 10.358 3.341m.708 9.987a2.97 2.97 0 0 1 1.875 0l2.179.723a1.68 1.68 0 0 1 1.14 1.519 5.62 5.62 0 0 1-8.512 0 1.68 1.68 0 0 1 1.14-1.519zm.938-4.94a2.059 2.059 0 0 1 0 4.118c-2.726-.094-2.723-4.02 0-4.117m3.326 5.026-1.912-.638a2.727 2.727 0 1 0-2.828 0l-1.912.638a2.35 2.35 0 0 0-1.46 1.428 5.55 5.55 0 0 1-.835-2.928c.307-7.455 10.93-7.459 11.242 0 0 1.035-.29 2.05-.836 2.928a2.35 2.35 0 0 0-1.459-1.428m2.13 5.31c-3.371 2.812-8.771 2.46-11.752-.765a1.662 1.662 0 0 1-2.355-1.5c.067-2.19 3.243-2.186 3.31 0 .003.404-.145.794-.415 1.095 2.763 2.921 7.702 3.225 10.788.649a.336.336 0 0 1 .424.52m-.116-2.265A1.663 1.663 0 0 1 19.5 14.88c1.721-4.204-.626-9.27-4.972-10.62a.336.336 0 0 1 .21-.637c4.74 1.473 7.286 7.027 5.347 11.59a1.64 1.64 0 0 1 .08 2.409 1.656 1.656 0 0 1-2.821-1.163"
      ></path>
    </g>
    <defs>
      <clipPath id="clip0_12068_1228">
        <path fill={props.fill || "#fff"} d="M0 0h24v24H0z"></path>
      </clipPath>
    </defs>
  </svg>
);

export default HrKnowledgeIcon;