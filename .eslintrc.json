{"extends": ["next/core-web-vitals"], "rules": {"import/order": ["warn", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "pathGroups": [{"pattern": "react", "group": "external", "position": "before"}, {"pattern": "next/**", "group": "external", "position": "before"}, {"pattern": "@/components/**", "group": "internal", "position": "before"}, {"pattern": "@/lib/**", "group": "internal", "position": "before"}, {"pattern": "@/types/**", "group": "internal", "position": "before"}, {"pattern": "@/styles/**", "group": "internal", "position": "after"}], "pathGroupsExcludedImportTypes": ["react"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "no-restricted-imports": ["error", {"patterns": [{"group": ["../../../*"], "message": "Use path mapping (@/) instead of relative imports with more than 2 levels"}]}]}}