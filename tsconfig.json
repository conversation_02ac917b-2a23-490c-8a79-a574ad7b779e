{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/components": ["./src/components"], "@/components/ui": ["./src/components/ui"], "@/components/features": ["./src/components/features"], "@/components/icons": ["./src/components/icons"], "@/lib": ["./src/lib"], "@/lib/utils": ["./src/lib/utils"], "@/lib/hooks": ["./src/lib/hooks"], "@/lib/stores": ["./src/lib/stores"], "@/lib/api": ["./src/lib/api"], "@/lib/constants": ["./src/lib/constants"], "@/types": ["./src/types"], "@/styles": ["./src/styles"], "@/assets": ["./src/assets"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}