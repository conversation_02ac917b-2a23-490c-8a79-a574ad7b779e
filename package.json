{"name": "knowladge-base-solution", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@shadcn/ui": "^0.0.4", "@stianlarsen/border-beam": "^1.0.11", "@tanstack/react-table": "^8.21.3", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.3", "lottie-react": "^2.4.1", "lucide-react": "^0.515.0", "motion": "^12.23.0", "next": "15.3.3", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "ogl": "^1.0.11", "prism-react-renderer": "^2.4.1", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "react-joyride": "^3.0.0-7", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "zustand": "^4.4.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/prop-types": "^15.7.15", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-table": "^7.7.20", "eslint": "^9.31.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}